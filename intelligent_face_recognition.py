#!/usr/bin/env python3
"""
Intelligent Face Recognition System with Performance Optimization

This module implements an intelligent face recognition system that optimizes performance
by only running expensive recognition operations when necessary (new faces, lost tracking).

Key Features:
- Only recognizes faces when tracking state changes
- Caches recognized identities by tracker ID
- Separates detection from recognition
- Maintains backward compatibility
- Follows SOLID principles and KISS methodology
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional, Tuple, Any
import time
import logging
import numpy as np

logger = logging.getLogger(__name__)

# === Core Data Structures ===

class TrackState(Enum):
    """Face tracking states for optimization decisions."""
    NEW = "new"           # First time seeing this face
    TRACKED = "tracked"   # Face is being tracked successfully
    LOST = "lost"         # Tracking was lost
    REAPPEARED = "reappeared"  # Face reappeared after being lost

@dataclass
class FaceIdentity:
    """Cached face identity information."""
    person_name: str
    confidence_score: float
    distance: float
    embedding: Optional[np.ndarray] = None
    last_seen: float = 0.0
    recognition_count: int = 0

@dataclass
class TrackingInfo:
    """Information about a tracked face."""
    tracker_id: int
    state: TrackState
    identity: Optional[FaceIdentity] = None
    frames_since_recognition: int = 0
    last_bbox: Optional[List[float]] = None

# === Abstract Interfaces ===

class RecognitionBackend(ABC):
    """Abstract interface for face recognition backends."""
    
    @abstractmethod
    def detect_faces(self, frame: np.ndarray) -> List[dict]:
        """Detect faces in frame. Returns list of {bbox, score}."""
        pass
    
    @abstractmethod
    def generate_embedding(self, frame: np.ndarray, bbox: List[float]) -> Optional[np.ndarray]:
        """Generate embedding for a face region."""
        pass
    
    @abstractmethod
    def recognize_embedding(self, embedding: np.ndarray) -> Optional[FaceIdentity]:
        """Recognize a face embedding. Returns identity or None."""
        pass

class CacheStrategy(ABC):
    """Abstract interface for caching strategies."""
    
    @abstractmethod
    def should_recognize(self, track_info: TrackingInfo) -> bool:
        """Decide if recognition should be performed for this track."""
        pass
    
    @abstractmethod
    def update_cache(self, tracker_id: int, identity: FaceIdentity) -> None:
        """Update cache with new identity information."""
        pass
    
    @abstractmethod
    def get_cached_identity(self, tracker_id: int) -> Optional[FaceIdentity]:
        """Get cached identity for tracker ID."""
        pass

# === Concrete Implementations ===

class HailoRecognitionBackend(RecognitionBackend):
    """Hailo-based recognition backend using existing FaceRecognizer."""
    
    def __init__(self, face_recognizer):
        self.recognizer = face_recognizer
    
    def detect_faces(self, frame: np.ndarray) -> List[dict]:
        """Use Hailo detector only (skip embedding generation)."""
        try:
            det_out = self.recognizer.detector.predict(frame)
        except Exception as e:
            logger.warning("Detector predict failed: %s", e)
            return []
        
        results = self.recognizer.__class__.__dict__['safe_get_results'](det_out) if hasattr(self.recognizer.__class__.__dict__, 'safe_get_results') else []
        faces = []
        
        for res in results:
            score = float(res.get("score", 0.0))
            if score < self.recognizer.min_score:
                continue
            
            raw_bbox = res.get("bbox") or res.get("box") or res.get("rectangle")
            if raw_bbox is not None:
                try:
                    a = np.asarray(raw_bbox, dtype=float).flatten()
                    if a.size == 4:
                        x1, y1, x2, y2 = a.tolist()
                        if (x2 - x1) <= 1.0: x2 = x1 + x2
                        if (y2 - y1) <= 1.0: y2 = y1 + y2
                        bbox = [int(round(x1)), int(round(y1)), int(round(x2)), int(round(y2))]
                        faces.append({"bbox": bbox, "score": score, "raw": res})
                except Exception:
                    continue
        
        return faces
    
    def generate_embedding(self, frame: np.ndarray, bbox: List[float]) -> Optional[np.ndarray]:
        """Generate embedding for face region."""
        try:
            # Import alignment functions - these will be available when this module is imported
            import sys
            import os
            sys.path.append(os.path.dirname(__file__))

            # These functions should be available from the main module
            align_face_to_112 = getattr(sys.modules.get('__main__'), 'align_face_to_112', None)
            safe_parse_landmarks = getattr(sys.modules.get('__main__'), 'safe_parse_landmarks', None)
            safe_extract_embedding = getattr(sys.modules.get('__main__'), 'safe_extract_embedding', None)
            safe_get_results = getattr(sys.modules.get('__main__'), 'safe_get_results', None)

            if not all([align_face_to_112, safe_extract_embedding, safe_get_results]):
                # Fallback: try to import from jk_face_recong_test
                try:
                    from jk_face_recong_test import align_face_to_112, safe_parse_landmarks, safe_extract_embedding, safe_get_results
                except ImportError:
                    logger.warning("Could not import alignment functions")
                    return None
            
            # Try to get landmarks from raw detection if available
            lm = None  # Could be enhanced to use landmarks if available
            aligned = align_face_to_112(frame, lm, bbox)
            
            emb_out = self.recognizer.embedder.predict(aligned)
            emb_res = safe_get_results(emb_out)
            if not emb_res:
                return None
            
            emb_vec = safe_extract_embedding(emb_res[0])
            if emb_vec is None:
                return None
            
            emb = np.asarray(emb_vec, dtype=float).flatten()
            nrm = np.linalg.norm(emb)
            if nrm <= 1e-8:
                return None
            
            return (emb / nrm).astype(float)
            
        except Exception as e:
            logger.debug("Embedding generation failed: %s", e)
            return None
    
    def recognize_embedding(self, embedding: np.ndarray) -> Optional[FaceIdentity]:
        """Recognize embedding using existing database."""
        try:
            matches = self.recognizer.recognize_embeddings(embedding)
            if not matches:
                return None
            
            top_meta, top_distance = matches[0]
            
            # Check distance threshold
            if top_distance > self.recognizer.max_distance:
                return FaceIdentity("unknown", 0.0, top_distance)
            
            # Extract person name
            if isinstance(top_meta, dict):
                name = top_meta.get("person") or top_meta.get("label") or "unknown"
            else:
                try:
                    name = top_meta[0].get("person") or "unknown"
                except Exception:
                    name = "unknown"
            
            confidence = max(0.0, 1.0 - top_distance)  # Convert distance to confidence
            
            return FaceIdentity(
                person_name=name,
                confidence_score=confidence,
                distance=top_distance,
                embedding=embedding,
                last_seen=time.time()
            )
            
        except Exception as e:
            logger.debug("Recognition failed: %s", e)
            return None

class SimpleCache(CacheStrategy):
    """Simple caching strategy with configurable parameters."""
    
    def __init__(self, 
                 rerecognition_interval: int = 30,  # frames
                 cache_timeout: float = 60.0):      # seconds
        self.rerecognition_interval = rerecognition_interval
        self.cache_timeout = cache_timeout
        self.cache: Dict[int, FaceIdentity] = {}
    
    def should_recognize(self, track_info: TrackingInfo) -> bool:
        """Decide if recognition should be performed."""
        # Always recognize new faces
        if track_info.state == TrackState.NEW:
            return True
        
        # Always recognize reappeared faces
        if track_info.state == TrackState.REAPPEARED:
            return True
        
        # For tracked faces, check if we need periodic re-recognition
        if track_info.state == TrackState.TRACKED:
            if track_info.identity is None:
                return True  # No identity cached yet
            
            # Check if enough frames have passed for re-recognition
            if track_info.frames_since_recognition >= self.rerecognition_interval:
                return True
            
            # Check if cache has expired
            time_since_recognition = time.time() - track_info.identity.last_seen
            if time_since_recognition > self.cache_timeout:
                return True
        
        return False
    
    def update_cache(self, tracker_id: int, identity: FaceIdentity) -> None:
        """Update cache with new identity."""
        identity.last_seen = time.time()
        identity.recognition_count += 1
        self.cache[tracker_id] = identity
    
    def get_cached_identity(self, tracker_id: int) -> Optional[FaceIdentity]:
        """Get cached identity."""
        return self.cache.get(tracker_id)
    
    def cleanup_expired(self) -> None:
        """Remove expired entries from cache."""
        current_time = time.time()
        expired_ids = [
            tid for tid, identity in self.cache.items()
            if current_time - identity.last_seen > self.cache_timeout
        ]
        for tid in expired_ids:
            del self.cache[tid]

# === Main Intelligent Recognition System ===

class IntelligentFaceRecognizer:
    """
    Intelligent face recognition system that optimizes performance by only
    running expensive recognition operations when necessary.

    Key optimizations:
    - Separates detection from recognition
    - Only recognizes new/lost/reappeared faces
    - Caches identities by tracker ID
    - Configurable recognition intervals
    """

    def __init__(self,
                 recognition_backend: RecognitionBackend,
                 cache_strategy: Optional[CacheStrategy] = None,
                 enable_optimization: bool = True):
        self.backend = recognition_backend
        self.cache = cache_strategy or SimpleCache()
        self.enable_optimization = enable_optimization

        # Track state management
        self.tracks: Dict[int, TrackingInfo] = {}
        self.frame_count = 0

        # Performance metrics
        self.stats = {
            'total_detections': 0,
            'recognition_calls': 0,
            'cache_hits': 0,
            'optimization_savings': 0
        }

    def process_frame(self, frame: np.ndarray, tracker_detections=None) -> Tuple[List[dict], Dict[str, Any]]:
        """
        Process a frame with intelligent recognition optimization.

        Args:
            frame: Input RGB frame
            tracker_detections: Optional supervision detections with tracker IDs

        Returns:
            Tuple of (detections_with_identity, performance_stats)
        """
        self.frame_count += 1

        if not self.enable_optimization:
            # Fallback to original behavior
            return self._process_frame_original(frame)

        # Step 1: Get face detections (always needed)
        face_detections = self.backend.detect_faces(frame)
        self.stats['total_detections'] += len(face_detections)

        if not face_detections:
            return [], self.stats.copy()

        # Step 2: Update tracking states
        if tracker_detections is not None:
            self._update_tracking_states(face_detections, tracker_detections)
        else:
            # No tracking - treat all as new faces
            self._handle_no_tracking(face_detections)

        # Step 3: Intelligent recognition
        results = []
        for i, detection in enumerate(face_detections):
            result = self._process_detection_intelligently(frame, detection, i)
            results.append(result)

        # Step 4: Cleanup expired cache entries periodically
        if self.frame_count % 100 == 0:
            self.cache.cleanup_expired()

        return results, self.stats.copy()

    def _update_tracking_states(self, detections: List[dict], tracker_detections) -> None:
        """Update tracking states based on supervision tracker results."""
        if not hasattr(tracker_detections, 'tracker_id') or tracker_detections.tracker_id is None:
            self._handle_no_tracking(detections)
            return

        current_tracker_ids = set()

        for i, detection in enumerate(detections):
            if i < len(tracker_detections.tracker_id):
                tracker_id = int(tracker_detections.tracker_id[i])
                current_tracker_ids.add(tracker_id)

                if tracker_id in self.tracks:
                    # Existing track
                    track_info = self.tracks[tracker_id]
                    track_info.state = TrackState.TRACKED
                    track_info.frames_since_recognition += 1
                    track_info.last_bbox = detection["bbox"]
                else:
                    # New track
                    self.tracks[tracker_id] = TrackingInfo(
                        tracker_id=tracker_id,
                        state=TrackState.NEW,
                        last_bbox=detection["bbox"]
                    )

        # Mark lost tracks
        for tracker_id in list(self.tracks.keys()):
            if tracker_id not in current_tracker_ids:
                if self.tracks[tracker_id].state != TrackState.LOST:
                    self.tracks[tracker_id].state = TrackState.LOST

    def _handle_no_tracking(self, detections: List[dict]) -> None:
        """Handle case where no tracking is available - treat all as new."""
        self.tracks.clear()  # Clear previous tracks
        for i, detection in enumerate(detections):
            # Use detection index as pseudo tracker ID
            self.tracks[i] = TrackingInfo(
                tracker_id=i,
                state=TrackState.NEW,
                last_bbox=detection["bbox"]
            )

    def _process_detection_intelligently(self, frame: np.ndarray, detection: dict, detection_idx: int) -> dict:
        """Process a single detection with intelligent recognition."""
        # Find corresponding track info
        track_info = None
        for tid, info in self.tracks.items():
            if info.last_bbox and self._bbox_overlap(detection["bbox"], info.last_bbox) > 0.5:
                track_info = info
                break

        if track_info is None:
            # Fallback: create temporary track info
            track_info = TrackingInfo(
                tracker_id=detection_idx,
                state=TrackState.NEW,
                last_bbox=detection["bbox"]
            )

        # Prepare result with detection info
        result = {
            "bbox": detection["bbox"],
            "score": detection["score"],
            "tracker_id": track_info.tracker_id,
            "state": track_info.state.value
        }

        # Check if we should perform recognition
        if self.cache.should_recognize(track_info):
            # Perform expensive recognition
            identity = self._recognize_face(frame, detection)

            if identity:
                self.cache.update_cache(track_info.tracker_id, identity)
                track_info.identity = identity
                track_info.frames_since_recognition = 0

                result.update({
                    "label": identity.person_name,
                    "confidence": identity.confidence_score,
                    "distance": identity.distance,
                    "emb": identity.embedding
                })
            else:
                result.update({
                    "label": "unknown",
                    "confidence": 0.0,
                    "distance": None,
                    "emb": None
                })

            self.stats['recognition_calls'] += 1
        else:
            # Use cached identity
            cached_identity = self.cache.get_cached_identity(track_info.tracker_id)
            if cached_identity:
                result.update({
                    "label": cached_identity.person_name,
                    "confidence": cached_identity.confidence_score,
                    "distance": cached_identity.distance,
                    "emb": cached_identity.embedding
                })
                self.stats['cache_hits'] += 1
            else:
                result.update({
                    "label": "unknown",
                    "confidence": 0.0,
                    "distance": None,
                    "emb": None
                })

            self.stats['optimization_savings'] += 1

        return result

    def _recognize_face(self, frame: np.ndarray, detection: dict) -> Optional[FaceIdentity]:
        """Perform face recognition for a detection."""
        # Generate embedding
        embedding = self.backend.generate_embedding(frame, detection["bbox"])
        if embedding is None:
            return None

        # Recognize embedding
        return self.backend.recognize_embedding(embedding)

    def _bbox_overlap(self, bbox1: List[float], bbox2: List[float]) -> float:
        """Calculate IoU overlap between two bounding boxes."""
        x1_1, y1_1, x2_1, y2_1 = bbox1
        x1_2, y1_2, x2_2, y2_2 = bbox2

        # Calculate intersection
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)

        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0

        intersection = (x2_i - x1_i) * (y2_i - y1_i)
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union = area1 + area2 - intersection

        return intersection / union if union > 0 else 0.0

    def _process_frame_original(self, frame: np.ndarray) -> Tuple[List[dict], Dict[str, Any]]:
        """Fallback to original processing without optimization."""
        # This would call the original detect_and_embed method
        # Implementation depends on how we integrate with existing code
        return [], self.stats.copy()

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        total_processed = self.stats['recognition_calls'] + self.stats['optimization_savings']
        optimization_ratio = (self.stats['optimization_savings'] / total_processed * 100) if total_processed > 0 else 0

        return {
            **self.stats,
            'optimization_ratio_percent': optimization_ratio,
            'total_processed': total_processed,
            'active_tracks': len(self.tracks)
        }

    def reset_stats(self) -> None:
        """Reset performance statistics."""
        self.stats = {
            'total_detections': 0,
            'recognition_calls': 0,
            'cache_hits': 0,
            'optimization_savings': 0
        }
